import React, { useState, useEffect } from 'react';
import { DeploymentDiagramProps } from './DeploymentDiagram';
import './DeploymentDiagram.less';

// 蓝绿部署图表组件
const BlueGreenDeploymentDiagram: React.FC<DeploymentDiagramProps> = ({
  className,
  onComplete,
  autoPlay = true,
  width = 800,
  height = 180, // 进一步减小高度
}) => {
  // 动画阶段状态
  const [stage, setStage] = useState(0);
  
  // 自动播放动画
  useEffect(() => {
    if (!autoPlay) return;
    
    // 各阶段的时间间隔
    const stageDelays = [1000, 2500, 4000];
    
    const timers = stageDelays.map((delay, index) => {
      return setTimeout(() => {
        setStage(index + 1);
        if (index === stageDelays.length - 1 && onComplete) {
          setTimeout(onComplete, 800);
        }
      }, delay);
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [autoPlay, onComplete]);

  return (
    <div className={`deployment-diagram blue-green-diagram ${className || ''}`}>
      <svg 
        width="100%" 
        height={height} 
        viewBox="0 0 800 180" 
        preserveAspectRatio="xMidYMid meet"
      >
        {/* 定义渐变和滤镜 */}
        <defs>
          <linearGradient id="blueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#40A9FF" stopOpacity="1" />
            <stop offset="100%" stopColor="#1890FF" stopOpacity="1" />
          </linearGradient>
          <linearGradient id="greenGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#73D13D" stopOpacity="1" />
            <stop offset="100%" stopColor="#52C41A" stopOpacity="1" />
          </linearGradient>
          <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" />
            <feOffset dx="1" dy="2" result="offsetblur" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.2" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.3" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* 时间线 */}
        <g className="timeline" transform="translate(0, 150)">
          <line x1="120" y1="0" x2="680" y2="0" stroke="#E8E8E8" strokeWidth="2" strokeLinecap="round" />
          
          {/* 步骤指示器 */}
          {[
            {x: 120, text: '准备部署', active: stage >= 0},
            {x: 280, text: '部署绿版本', active: stage >= 1},
            {x: 440, text: '切换流量', active: stage >= 2},
            {x: 600, text: '下线蓝版本', active: stage >= 3}
          ].map((step, idx) => (
            <g key={idx} transform={`translate(${step.x}, 0)`}>
              <circle 
                r="4" 
                fill={step.active ? (idx === 3 ? '#52C41A' : '#1890FF') : '#F0F0F0'} 
                stroke={step.active ? 'none' : '#D9D9D9'}
                strokeWidth="1"
                filter={step.active ? 'url(#glow)' : 'none'}
              />
              <text 
                y="15" 
                textAnchor="middle" 
                fill={step.active ? '#262626' : '#8C8C8C'} 
                fontSize="8"
                fontWeight={step.active ? 'bold' : 'normal'}
              >
                {step.text}
              </text>
            </g>
          ))}
          
          {/* 已完成的连接线 */}
          {stage >= 1 && <line x1="120" y1="0" x2="280" y2="0" stroke="#1890FF" strokeWidth="2" strokeLinecap="round" className="animated-path" />}
          {stage >= 2 && <line x1="280" y1="0" x2="440" y2="0" stroke="#1890FF" strokeWidth="2" strokeLinecap="round" className="animated-path" />}
          {stage >= 3 && <line x1="440" y1="0" x2="600" y2="0" stroke="#52C41A" strokeWidth="2" strokeLinecap="round" className="animated-path" />}
        </g>

        {/* 初始状态 - 只有蓝版本 */}
        <g className={`stage-0 ${stage >= 0 ? 'active' : ''}`}>
          <g className="server-group" transform="translate(200, 30)">
            <rect 
              x="0" 
              y="0" 
              width="70" 
              height="60" 
              rx="5" 
              fill="url(#blueGradient)" 
              filter="url(#dropShadow)"
              className={stage >= 2 ? 'faded' : ''}
            />
            <text x="35" y="20" textAnchor="middle" fill="white" fontSize="10" fontWeight="bold">蓝版本</text>
            <text x="35" y="35" textAnchor="middle" fill="white" fontSize="9">V1.0</text>
            <text x="35" y="48" textAnchor="middle" fill="white" fontSize="8" opacity="0.9">当前生产环境</text>
            
            {/* 流量指示器 */}
            <g transform="translate(35, 70)">
              <rect x="-25" y="0" width="50" height="12" rx="6" fill="white" stroke="#1890FF" strokeWidth="1" />
              <text x="0" y="9" textAnchor="middle" fill="#1890FF" fontSize="8" fontWeight="bold">
                流量: {stage < 2 ? '100%' : '0%'}
              </text>
            </g>
          </g>
          
          {/* 用户图标 */}
          <g className="users" transform="translate(80, 60)">
            <circle r="14" fill="#F0F0F0" />
            <text x="0" y="4" textAnchor="middle" fontSize="12">👥</text>
            <text x="0" y="24" textAnchor="middle" fontSize="8" fill="#8C8C8C">用户</text>
          </g>
          
          {/* 流量路径 */}
          <g className={`traffic-path ${stage < 2 ? 'active' : ''}`}>
            <path 
              d="M98,60 C130,60 160,60 180,60" 
              stroke="#1890FF" 
              strokeWidth="2" 
              strokeDasharray="5,3"
              markerEnd="url(#blueArrow)" 
            />
            <defs>
              <marker id="blueArrow" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="5" markerHeight="5" orient="auto">
                <path d="M 0 0 L 10 5 L 0 10 z" fill="#1890FF" />
              </marker>
            </defs>
          </g>
        </g>
        
        {/* 阶段1 - 部署绿版本 */}
        {stage >= 1 && (
          <g className="stage-1 animated-element delay-1">
            <g className="server-group" transform="translate(480, 30)">
              <rect 
                x="0" 
                y="0" 
                width="70" 
                height="60" 
                rx="5" 
                fill="url(#greenGradient)" 
                filter="url(#dropShadow)"
              />
              <text x="35" y="20" textAnchor="middle" fill="white" fontSize="10" fontWeight="bold">绿版本</text>
              <text x="35" y="35" textAnchor="middle" fill="white" fontSize="9">V2.0</text>
              <text x="35" y="48" textAnchor="middle" fill="white" fontSize="8" opacity="0.9">
                {stage < 2 ? '部署中' : '新生产环境'}
              </text>
              
              {/* 流量指示器 */}
              <g transform="translate(35, 70)">
                <rect x="-25" y="0" width="50" height="12" rx="6" fill="white" stroke="#52C41A" strokeWidth="1" />
                <text x="0" y="9" textAnchor="middle" fill="#52C41A" fontSize="8" fontWeight="bold">
                  流量: {stage < 2 ? '0%' : '100%'}
                </text>
              </g>
            </g>
            
            {/* 部署箭头 */}
            <g className="deployment-arrow animated-path">
              <path 
                d="M290,35 C350,30 400,30 460,35" 
                stroke="#8C8C8C" 
                strokeWidth="1.5" 
                strokeDasharray="5,3" 
                fill="none" 
              />
              <polygon points="460,32 468,35 460,38" fill="#8C8C8C" className="animated-element delay-2" />
              <text x="375" y="20" textAnchor="middle" fill="#8C8C8C" fontSize="8" className="animated-element delay-1">部署新版本</text>
            </g>
          </g>
        )}
        
        {/* 阶段2 - 切换流量 */}
        {stage >= 2 && (
          <g className="stage-2 animated-element delay-1">
            {/* 新的流量路径 */}
            <path
              d="M98,60 C170,60 320,60 460,60"
              stroke="#52C41A"
              strokeWidth="2"
              strokeDasharray="5,3"
              markerEnd="url(#greenArrow)"
              className="animated-path"
            />
            <defs>
              <marker id="greenArrow" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="5" markerHeight="5" orient="auto">
                <path d="M 0 0 L 10 5 L 0 10 z" fill="#52C41A" />
              </marker>
            </defs>

            {/* 切换指示器 - 位置在流量路径的真正中点 (98+460)/2=279, Y坐标与流量路径一致 */}
            <g className="switch-indicator animated-element delay-2" transform="translate(279, 60)">
              <circle r="10" fill="#F5F5F5" stroke="#D9D9D9" strokeWidth="1" />
              <text x="0" y="3" textAnchor="middle" fontSize="9">⚡</text>
              <text x="0" y="-15" textAnchor="middle" fontSize="8" fill="#262626">流量切换</text>
            </g>
          </g>
        )}
        
        {/* 阶段3 - 下线蓝版本 */}
        {stage >= 3 && (
          <g className="stage-3 animated-element delay-2">
            {/* 下线指示 */}
            <g className="shutdown-indicator" transform="translate(235, 15)">
              <circle r="8" fill="#F5F5F5" stroke="#D9D9D9" strokeWidth="1" />
              <text x="0" y="3" textAnchor="middle" fontSize="7">🔌</text>
            </g>

            {/* 调整连接线路径，避免与其他元素重叠 */}
            <path
              d="M245,15 C265,12 285,10 305,8"
              stroke="#D9D9D9"
              strokeWidth="1.5"
              strokeDasharray="3,3"
              fill="none"
              className="animated-path"
            />

            {/* 调整文本位置，避免与流量切换元素重叠 */}
            <text x="380" y="12" textAnchor="middle" fill="#8C8C8C" fontSize="8" className="animated-element delay-3">蓝版本下线，释放资源</text>
          </g>
        )}
      </svg>
    </div>
  );
};

export default BlueGreenDeploymentDiagram; 